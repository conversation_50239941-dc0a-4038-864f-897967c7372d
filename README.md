# Image Gemini Chat

A web application that allows you to chat with Google's Gemini AI about your database and upload images for analysis.

## Project Structure

- `src/`: React frontend application
- `api/`: Flask backend API that connects to Google's Gemini AI
- `public/`: Static assets and HTML template

## Getting Started

### Setup Backend

1. Install Python dependencies:

```sh
cd api
pip install -r requirements.txt
```

2. Run the Flask server:

```sh
cd api
python app.py
```

The backend API will run on http://localhost:5000

### Setup Frontend

1. Install Node.js dependencies:

```sh
npm install
```

2. Start the development server:

```sh
npm run dev
```

The frontend application will run on http://localhost:8080

## Features

- Chat with Gemini AI about your database
- Voice input with speech recognition
- Image upload and analysis
- Responsive design for desktop and mobile devices

## API Endpoints

- `GET /api/health`: Health check endpoint
- `POST /api/chat`: Send a message to the Gemini AI model

## Technologies

- Frontend:
  - React
  - TypeScript
  - Vite
  - Shadcn/UI
  - Tailwind CSS

- Backend:
  - Flask
  - Google's Vertex AI SDK
  - Gemini 2.0 Flash

## Configuration

The application uses the following configuration:
- Project ID: voice-460210
- Location: us-central1
- Model: gemini-2.0-flash-001
- Data Store: projects/voice-460210/locations/global/collections/default_collection/dataStores/datastore_1747736246085



## Production Deployment

### System Services

The application runs using systemd services:

```sh
# Start services
sudo systemctl start gemini-api.service

# Check service status
sudo systemctl status gemini-api.service

# Restart services after code changes
sudo systemctl restart gemini-api.service

# Stop services
sudo systemctl stop gemini-api.service
```

### Nginx Configuration

The application is served using Nginx with SSL:

- Domain: askurdata.com
- Frontend: Static files from `/home/<USER>/apps/new_try/image-gemini-chat/dist`
- Backend API: Proxied to the Flask server on port 5000
- SSL: Using Let's Encrypt certificates for askurdata.com

### Updating the Application

1. Pull latest code changes
2. For backend changes:
   ```sh
   sudo systemctl restart gemini-api.service
   ```
3. For frontend changes:
   ```sh
   # Rebuild the frontend
   npm run build
   ```

### Troubleshooting

- Check Nginx logs: `sudo tail -f /var/log/nginx/error.log`
- Check API service logs: `sudo journalctl -u gemini-api.service`
- Verify Nginx configuration: `sudo nginx -t`
- Restart Nginx after config changes: `sudo systemctl restart nginx`

> **Important**: Always use relative API URLs (/api) in frontend code, not hardcoded localhost URLs
