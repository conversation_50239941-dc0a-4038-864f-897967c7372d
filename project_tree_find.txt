
|____package.json
|____README.md
|____dist
| |____favicon.png
| |____placeholder.svg
| |____robots.txt
| |____index.html
| |____favicon.ico
| |____assets
| | |____index-C5lskmke.js
| | |____index-n7UBUpzC.css
|____api
| |____api.log
| |____requirements.txt
| |____app.py
|____ssl
| |____key.pem
| |____cert.pem
|____tsconfig.app.json
|____eslint.config.js
|____public
| |____favicon.png
| |____placeholder.svg
| |____robots.txt
| |____favicon.ico
|____.git
| |____logs
| | |____refs
| | | |____heads
| | | | |____main
| | | |____remotes
| | | | |____origin
| | | | | |____HEAD
| | |____HEAD
| |____refs
| | |____tags
| | |____heads
| | | |____main
| | |____remotes
| | | |____origin
| | | | |____HEAD
| |____HEAD
| |____objects
| | |____info
| | |____pack
| | | |____pack-e376d24e4dfbfb55c76fb56e05ddff162ae9f1e5.idx
| | | |____pack-e376d24e4dfbfb55c76fb56e05ddff162ae9f1e5.pack
| |____branches
| |____hooks
| | |____push-to-checkout.sample
| | |____pre-rebase.sample
| | |____pre-applypatch.sample
| | |____prepare-commit-msg.sample
| | |____pre-push.sample
| | |____fsmonitor-watchman.sample
| | |____commit-msg.sample
| | |____update.sample
| | |____pre-merge-commit.sample
| | |____post-update.sample
| | |____pre-commit.sample
| | |____pre-receive.sample
| | |____applypatch-msg.sample
| |____FETCH_HEAD
| |____index
| |____description
| |____config
| |____packed-refs
| |____info
| | |____exclude
|____src
| |____hooks
| | |____use-mobile.tsx
| | |____use-toast.ts
| |____types
| | |____speech-recognition.d.ts
| |____lib
| | |____utils.ts
| |____App.tsx
| |____vite-env.d.ts
| |____main.tsx
| |____pages
| | |____NotFound.tsx
| | |____Index.tsx
| |____components
| | |____SqlResults.tsx
| | |____Chat.tsx
| | |____EmptyState.tsx
| | |____ChatInput.tsx
| | |____Sidebar.tsx
| | |____ChatMessage.tsx
| | |____ui
| | | |____radio-group.tsx
| | | |____progress.tsx
| | | |____aspect-ratio.tsx
| | | |____card.tsx
| | | |____calendar.tsx
| | | |____hover-card.tsx
| | | |____badge.tsx
| | | |____checkbox.tsx
| | | |____breadcrumb.tsx
| | | |____accordion.tsx
| | | |____button.tsx
| | | |____chart.tsx
| | | |____command.tsx
| | | |____pagination.tsx
| | | |____sonner.tsx
| | | |____resizable.tsx
| | | |____drawer.tsx
| | | |____sheet.tsx
| | | |____table.tsx
| | | |____toggle-group.tsx
| | | |____dropdown-menu.tsx
| | | |____toast.tsx
| | | |____alert.tsx
| | | |____label.tsx
| | | |____toaster.tsx
| | | |____form.tsx
| | | |____tooltip.tsx
| | | |____carousel.tsx
| | | |____collapsible.tsx
| | | |____navigation-menu.tsx
| | | |____select.tsx
| | | |____textarea.tsx
| | | |____separator.tsx
| | | |____dialog.tsx
| | | |____alert-dialog.tsx
| | | |____input.tsx
| | | |____popover.tsx
| | | |____use-toast.ts
| | | |____input-otp.tsx
| | | |____toggle.tsx
| | | |____switch.tsx
| | | |____scroll-area.tsx
| | | |____sidebar.tsx
| | | |____menubar.tsx
| | | |____avatar.tsx
| | | |____tabs.tsx
| | | |____context-menu.tsx
| | | |____slider.tsx
| | | |____skeleton.tsx
| | |____ChatLayout.tsx
| |____services
| | |____ApiService.ts
| |____App.css
| |____index.css
|____instruction.txt
|____tsconfig.json
|____tailwind.config.ts
|____tsconfig.node.json
|____index.html
|____.gitignore
|____voice-460210-9af49b6dc973.json
|____components
|____vite.config.ts