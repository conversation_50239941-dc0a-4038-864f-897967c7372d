import vertexai
from vertexai.generative_models import GenerativeModel, Part, Tool, grounding
import json 
from google.protobuf.json_format import MessageToDict 
import os

PROJECT_ID = "voice-460210"
LOCATION = "us-central1"
MODEL_NAME = "gemini-2.0-flash-001" 
DATA_STORE_PATH = "projects/voice-460210/locations/global/collections/default_collection/dataStores/datastore_1747736246085"

try:
    vertexai.init(project=PROJECT_ID, location=LOCATION)
    print(f"Vertex AI SDK initialized successfully for project {PROJECT_ID} in {LOCATION}.")
except Exception as e:
    print(f"Error initializing Vertex AI SDK: {e}")
    exit()

try:
    search_tool = Tool.from_retrieval(
        retrieval=grounding.Retrieval(
            source=grounding.VertexAISearch(
                datastore=DATA_STORE_PATH
            ),
        )
    )
    print(f"Grounding tool configured for Data Store: {DATA_STORE_PATH}")
except Exception as e:
    print(f"Error configuring grounding tool: {e}")
    exit()

def get_system_instruction():
    instruction_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "instruction.txt")
    with open(instruction_path, 'r') as f:
        return f.read()

system_instruction = get_system_instruction()

# --- إنشاء نسخة من النموذج التوليدي مع تعليمات النظام ---
try:
    model = GenerativeModel(
        MODEL_NAME,
        system_instruction=system_instruction  # هنا نمرر تعليمات النظام
    )
    print(f"Generative model {MODEL_NAME} loaded with system instruction.")
except Exception as e:
    print(f"Error loading generative model {MODEL_NAME}: {e}")
    exit()


def ask_question_with_grounding(question_prompt: str):
    print(f"\nإرسال السؤال: \"{question_prompt}\"")
    print(f"يتم استخدام النموذج: {MODEL_NAME}")
    print(f"مع الربط بـ Data Store: {DATA_STORE_PATH}\n")

    generated_text = "" 

    try:
        response = model.generate_content(
            [question_prompt],
            tools=[search_tool]
        )

        print("--- الإجابة من النموذج ---")
        if response.candidates and response.candidates[0].content.parts:
            # Handle multi-part responses (text and code blocks)
            try:
                all_content = ""
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'text'):
                        all_content += part.text
                    elif hasattr(part, 'function_call'):
                        all_content += f"\nFunction call: {part.function_call}\n"
                    else:
                        all_content += f"\nUnknown content part type: {type(part)}\n"
                print(all_content)
            except Exception as content_error:
                print(f"Error extracting content: {content_error}")
                # Fallback to debug output
                print("Raw response data:")
                print(MessageToDict(response._pb))
        else:
            print("لم يتمكن النموذج من توليد إجابة.")
            if response.candidates and response.candidates[0].finish_reason:
                print(f"  سبب الإنهاء: {response.candidates[0].finish_reason.name}")
            if response.prompt_feedback:
                print(f"  تقييم الطلب: {response.prompt_feedback.block_reason}")
            return

    except Exception as e:
        print(f"حدث خطأ أثناء توليد الإجابة: {e}")
        exit()
      
if __name__ == "__main__":
    while True:
        user_input = input("\nاطرح سؤالك (أو اكتب 'خروج' للإنهاء): ")
        if user_input.lower() == 'خروج':
            break
        if user_input:
            ask_question_with_grounding(user_input)