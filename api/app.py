import os
import sys
import logging
import traceback
from flask import Flask, request, jsonify
from flask_cors import CORS
import vertexai
from vertexai.generative_models import GenerativeModel, Part, Tool, grounding, ChatSession, Content
import json
from google.protobuf.json_format import MessageToDict
import time
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import base64
import tempfile

# --- Configuration ---
# إعداد التسجيل (Logging Setup)
logging.basicConfig(
    level=logging.INFO, # Changed to INFO for less verbosity in normal operation, use DEBUG for deep dive
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("api.log"),
        logging.StreamHandler()
    ]
)

# Add the parent directory to the path so we can import from there
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Database configuration
DB_CONFIG = {
    'dbname': os.getenv('POSTGRES_DB', 'quickbooks'),
    'user': os.getenv('POSTGRES_USER', 'moutaz'),
    'password': os.getenv('POSTGRES_PASSWORD', 'root'),
    'host': os.getenv('POSTGRES_HOST', 'localhost'),
    'port': os.getenv('POSTGRES_PORT', '5432')
}

def get_db_connection():
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logging.error(f"Database connection error: {e}")
        raise

# Gemini AI configuration
PROJECT_ID = "voice-460210"
LOCATION = "us-central1"
MODEL_NAME = "gemini-2.5-flash-preview-05-20"
DATA_STORE_PATH = "projects/voice-460210/locations/global/collections/default_collection/dataStores/datastore_1747736246085"

# Initialize Vertex AI
try:
    vertexai.init(project=PROJECT_ID, location=LOCATION)
    logging.info(f"Vertex AI SDK initialized successfully for project {PROJECT_ID} in {LOCATION}.")
except Exception as e:
    logging.critical(f"Error initializing Vertex AI SDK: {e}")
    sys.exit(1)

# Set up the grounding tool
try:
    search_tool = Tool.from_retrieval(
        retrieval=grounding.Retrieval(
            source=grounding.VertexAISearch(
                datastore=DATA_STORE_PATH
            ),
        )
    )
    logging.info(f"Grounding tool configured for Data Store: {DATA_STORE_PATH}")
    # logging.debug(f"Search tool details: {search_tool.to_dict()}") # Uncomment for more verbose output

except Exception as e:
    logging.critical(f"Error configuring grounding tool: {e}")
    sys.exit(1)

# Load system instructions from instruction.txt
def get_system_instruction():
    # Look for instruction.txt in the same directory as app.py
    instruction_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "instruction.txt")

    try:
        with open(instruction_path, 'r', encoding='utf-8') as f:
            instruction_content = f.read()
            logging.info(f"Successfully loaded instruction.txt ({len(instruction_content)} chars)")
            return instruction_content
    except FileNotFoundError:
        logging.error(f"instruction.txt not found at {instruction_path}")
        return "You are a helpful AI assistant that helps users with their database questions. Answer concisely and accurately."
    except Exception as e:
        logging.error(f"Error reading instruction.txt: {e}")
        return "You are a helpful AI assistant that helps users with their database questions. Answer concisely and accurately."

# Load the instructions immediately when the app starts
system_instruction_sql_assistant = get_system_instruction()
logging.info(f"System instruction loaded: {system_instruction_sql_assistant[:100]}...")

# Constants for session management
RESET_AFTER_N_TURNS = 3 # Reset session after this many user-AI turns (e.g., 3 turns = 6 history parts)
# Dictionary to store chat sessions (now holds model instance, chat session, and turn count)
chat_sessions = {}

# --- Helper Functions ---

def create_new_generative_model(system_instr_content, last_questions=None):
    """Creates a new GenerativeModel instance with specified system instructions and generation config."""
    try:
        # Prepare system instruction with last questions if provided
        final_system_instruction = system_instr_content
        if last_questions and len(last_questions) > 0:
            questions_context = "Here are the last 3 questions from my old conversation to help you understand my new question if it's related:\n"
            for i, q in enumerate(last_questions):
                # Add a simple indicator like "recent", "earlier", "much earlier"
                time_indicator = "recent" if i == len(last_questions)-1 else ("earlier" if i == len(last_questions)-2 else "much earlier")
                questions_context += f"{i+1}. [{time_indicator}] {q}\n"
            questions_context += "\nThat's my conversation history. For my new question, ALWAYS use vertex_ai_search to look up names before responding."
            final_system_instruction = f"{system_instr_content}\n\n{questions_context}"

        # Create the model with proper system instruction format
        new_model = GenerativeModel(
            MODEL_NAME,
            # Fix: Use a string directly instead of Content object
            system_instruction=final_system_instruction,
            generation_config={
                "temperature": 0.1,
                "top_p": 1,
                "top_k": 1,
                "max_output_tokens": 8192,
            }
        )
        logging.info(f"New GenerativeModel instance {MODEL_NAME} created with last {len(last_questions) if last_questions else 0} questions.")
        return new_model
    except Exception as e:
        logging.error(f"Error creating new generative model {MODEL_NAME}: {e}", exc_info=True)
        raise

def cleanup_old_sessions():
    """Removes inactive chat sessions (older than 1 hour)."""
    current_time = time.time()
    sessions_to_remove = []
    for session_id, session_data in chat_sessions.items():
        if current_time - session_data['last_activity'] > 3600: # 1 hour
            sessions_to_remove.append(session_id)

    for session_id in sessions_to_remove:
        del chat_sessions[session_id]
        logging.info(f"Removed inactive session: {session_id}")
    return len(sessions_to_remove)

def generate_summary_from_history(history_parts):
    """Generates a concise summary of the conversation history using a dedicated model."""
    if not history_parts:
        return "No prior conversation."

    # Use a separate model instance for summarization with its own system instruction
    # This prevents the summarizer from trying to generate SQL or being the main assistant
    try:
        summary_model = create_new_generative_model(
            "You are a helpful assistant. Summarize the provided conversation history concisely, focusing on core facts, entities, and open questions relevant to a database query assistant. Do not generate SQL. The summary should be brief and informative."
        )
        summary_chat_session = summary_model.start_chat(history=history_parts)

        summary_prompt = "Summarize the above conversation history concisely."
        summary_response = summary_chat_session.send_message(summary_prompt)

        # Extract text from response (handle potential candidates/parts)
        summary_text = ""
        if hasattr(summary_response, 'candidates') and summary_response.candidates:
            for candidate in summary_response.candidates:
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    parts_text = [part.text for part in candidate.content.parts if hasattr(part, 'text')]
                    summary_text = "\n".join(parts_text)
                    break # Take the first candidate's text
        if not summary_text and hasattr(summary_response, 'text'): # Fallback
            summary_text = summary_response.text

        logging.info(f"Generated summary: {summary_text[:150]}...")
        return summary_text.strip() if summary_text else "Previous conversation context is unavailable."
    except Exception as e:
        logging.warning(f"Failed to generate summary: {e}. Proceeding without detailed summary.", exc_info=True)
        return "Previous conversation context is unavailable."

# Add this function to help with consistent name lookup
def ensure_thorough_name_lookup(prompt, model_instance, search_tool):
    """
    Adds a preprocessing step to ensure thorough name lookup before main query.
    This helps ensure consistent name resolution across repeated queries.
    """
    try:
        # Extract potential names from the prompt
        preprocessing_prompt = "Extract all potential entity names (customers, suppliers, items) from this query. Return ONLY a JSON array of names, nothing else:\n\n" + prompt

        # Get potential names
        names_response = model_instance.generate_content(preprocessing_prompt)
        names_text = names_response.text if hasattr(names_response, 'text') else ""

        # If we got names, ensure they're looked up thoroughly
        if names_text and (names_text.startswith('[') or names_text.startswith('{')):
            try:
                # Try to parse as JSON
                names_data = json.loads(names_text)
                names = names_data if isinstance(names_data, list) else [names_data]

                # For each name, explicitly force lookup with variations
                lookup_results = []
                for name in names:
                    if isinstance(name, str):
                        # Simple lookup prompt without embedded instructions
                        lookup_prompt = f"Search for this name: \"{name}\". Use vertex_ai_search tool. Return ONLY the exact matched name if found, or \"NOT_FOUND\" if not found."

                        # Force lookup for this name
                        lookup_response = model_instance.generate_content(
                            lookup_prompt,
                            tools=[search_tool]
                        )
                        lookup_text = lookup_response.text if hasattr(lookup_response, 'text') else "NOT_FOUND"
                        lookup_results.append(f"{name}: {lookup_text}")

                # Add lookup results to the original prompt
                enhanced_prompt = f"I've already searched for these names with the following results:\n{', '.join(lookup_results)}\n\nNow answer this query using the exact matched names from above:\n{prompt}"

                return enhanced_prompt
            except Exception as e:
                logging.warning(f"Error in name preprocessing: {e}")

        # If anything fails, return the original prompt
        return prompt
    except Exception as e:
        logging.error(f"Error in ensure_thorough_name_lookup: {e}")
        return prompt  # Return original prompt if anything fails

# Add this function to estimate token usage
def estimate_token_usage(prompt_text, response_text):
    """
    Estimates token usage for a prompt and response.
    Uses a more accurate estimation based on GPT tokenization patterns.
    """
    # More accurate estimation based on typical tokenization patterns
    def count_tokens(text):
        # Check if text contains significant non-ASCII (likely Arabic)
        non_ascii_ratio = sum(1 for c in text if ord(c) > 127) / max(len(text), 1)

        # Adjust ratio based on content type
        if non_ascii_ratio > 0.3:
            # Arabic and other non-Latin scripts use fewer chars per token
            chars_per_token = 2.0
        else:
            # For English/Latin text:
            # - Spaces and punctuation often get their own tokens
            # - Common words are single tokens despite length
            # - Longer/uncommon words split into multiple tokens
            chars_per_token = 4.0

            # Adjust for code blocks which tokenize differently
            if "```" in text or "SELECT" in text or "FROM" in text:
                # SQL and code blocks tend to have more tokens per character
                chars_per_token = 3.5

        # Calculate estimated tokens
        estimated_tokens = max(1, int(len(text) / chars_per_token))

        # Add system instruction overhead for first message
        if "instruction.txt" in text or len(text) > 5000:
            # If this appears to be a system instruction or very long text
            # Use the known ratio from your measurement (9394 chars ≈ 2140 tokens)
            if len(text) > 9000:  # If similar to instruction.txt size
                estimated_tokens = int(len(text) / 4.4)

        return estimated_tokens

    prompt_tokens = count_tokens(prompt_text)
    response_tokens = count_tokens(response_text)

    return {
        "prompt_tokens": prompt_tokens,
        "response_tokens": response_tokens,
        "total_tokens": prompt_tokens + response_tokens
    }

# --- API Routes ---

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        # Run cleanup to remove old sessions occasionally
        if len(chat_sessions) > 0 and time.time() % 60 < 1: # Run roughly every minute
            removed = cleanup_old_sessions()
            if removed > 0:
                logging.info(f"Cleaned up {removed} inactive chat sessions.")

        data = request.json
        user_message = data.get('message', '') # Renamed for clarity
        conversation_id = data.get('conversation_id', 'default')
        timestamp = data.get('timestamp', int(time.time() * 1000))
        is_retry = data.get('is_retry', False)  # Check if this is a retry request
        retry_count = data.get('retry_count', 0)  # Get retry count

        if not user_message:
            return jsonify({"error": "No message provided"}), 400

        active_chat_session_data = chat_sessions.get(conversation_id)
        last_questions = [] # Store last 3 user questions
        is_new_conversation = False # Flag for initial context

        if active_chat_session_data:
            # Extract last 3 user questions from history
            if active_chat_session_data.get('history', []):
                last_questions = active_chat_session_data.get('last_questions', [])
                logging.info(f"Retrieved {len(last_questions)} previous questions for context")
            active_chat_session_data['last_activity'] = time.time()
        else:
            # First message for this conversation_id
            is_new_conversation = True
            logging.info(f"Starting new conversation: {conversation_id}")
            chat_sessions[conversation_id] = {
                'last_activity': time.time(),
                'created_at': datetime.now().isoformat(),
                'history': [],
                'last_questions': []
            }
            active_chat_session_data = chat_sessions[conversation_id]

        # Create a new model for every message with last questions context
        logging.info(f"Creating new model for message in conversation {conversation_id}")
        new_model = create_new_generative_model(system_instruction_sql_assistant, last_questions)
        active_chat_session = new_model.start_chat()

        # Update the session data with the new model and chat session
        active_chat_session_data['model_instance'] = new_model
        active_chat_session_data['chat_session'] = active_chat_session

        try:
            # If this is a retry, modify the prompt to encourage alternative approaches
            if is_retry:
                logging.info(f"Processing retry request (count: {retry_count}) for conversation {conversation_id}")

                # Create retry instructions based on retry count
                retry_instructions = [
                    "Try a different approach to find this information. The previous query returned no results.",
                    "The previous search found no results. Try alternative spellings, transliterations, or broader terms.",
                    "No results were found previously. Try a completely different approach or SQL query structure."
                ]

                retry_instruction = retry_instructions[min(retry_count, len(retry_instructions) - 1)]

                # Add special instructions for retry
                final_prompt_for_gemini = f"""
                {retry_instruction}

                Original query: {user_message}
                """

                # Add the strict adherence reminder for retry
                final_prompt_for_gemini = f"Strictly apply your core instructions, especially for name lookup, transliteration, and contextual matching. This is a RETRY attempt, so be more flexible with name matching. Answer: {final_prompt_for_gemini}"
            else:
                # Construct the full message for the current turn
                final_prompt_for_gemini = user_message # Start with the user's direct message

                # Add general context / initial instructions for brand new or reset sessions
                if is_new_conversation:
                    current_date = datetime.now().strftime("%Y-%m-%d")
                    initial_context_str = f"""Today's date is {current_date}.
You are working with a PostgreSQL database. Use PostgreSQL date functions (EXTRACT, DATE_TRUNC, etc.). Prefer using "WITH" in queries."""
                    # Prepend the initial context
                    final_prompt_for_gemini = f"{initial_context_str}\n\n{final_prompt_for_gemini}"
                    logging.info(f"Added initial context to first prompt for {conversation_id}.")

                # Add the strict adherence reminder for every turn (after initial context if applicable)
                final_prompt_for_gemini = f"Strictly apply your core instructions, especially for name lookup, transliteration, and contextual matching. Answer: {final_prompt_for_gemini}"

                # Store the current question in history
                if not is_retry:
                    # Update last_questions (keep last 3)
                    if len(last_questions) >= 3:
                        last_questions.pop(0)  # Remove oldest
                    last_questions.append(user_message)
                    active_chat_session_data['last_questions'] = last_questions

                    # Update history (keep last 10)
                    history = active_chat_session_data.get('history', [])
                    if len(history) >= 10:
                        history.pop(0)  # Remove oldest
                    history.append(user_message)
                    active_chat_session_data['history'] = history
                    logging.debug(f"Updated history for {conversation_id}, now has {len(active_chat_session_data['history'])} items")

            # NEW: Ensure thorough name lookup before sending the main prompt
            enhanced_prompt = ensure_thorough_name_lookup(final_prompt_for_gemini, new_model, search_tool, is_retry=is_retry)
            logging.info(f"Enhanced prompt with thorough name lookup for {conversation_id}")

            # Send to Gemini
            response = active_chat_session.send_message(
                enhanced_prompt,
                tools=[search_tool]
            )

            # Update turn count (only if not a retry)
            if not is_retry:
                active_chat_session_data['turn_count'] = active_chat_session_data.get('turn_count', 0) + 1
                logging.debug(f"Conversation {conversation_id} turn count incremented to {active_chat_session_data['turn_count']}")

            # Extract text from response
            response_text = ""
            if hasattr(response, 'candidates') and response.candidates:
                for candidate in response.candidates:
                    if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                        parts_text = [part.text for part in candidate.content.parts if hasattr(part, 'text')]
                        response_text = "\n".join(parts_text)
                        break # Take the first candidate's text
            if not response_text and hasattr(response, 'text'): # Fallback
                response_text = response.text

            if not response_text:
                response_text = "Sorry, I couldn't generate a proper response."

            # Check if the response indicates "No results" and retry if needed
            no_results_indicators = [
                "No records found matching your query",
                "No results found for this query.",
                "0 rows returned",
                "No data found",
                "No matching records",
                "No results found"  # Added a simpler version
            ]

            # Log the first 200 chars of the response for debugging
            logging.info(f"Response for {conversation_id}: '{response_text[:200]}...'")

            contains_no_results = False
            matching_indicator = None

            # Check each indicator and log which one matched
            for indicator in no_results_indicators:
                if indicator.lower() in response_text.lower():  # Case-insensitive check
                    contains_no_results = True
                    matching_indicator = indicator
                    break

            if contains_no_results:
                logging.info(f"No results detected in response for {conversation_id}. Matched indicator: '{matching_indicator}'")

            # Auto-retry logic
            if contains_no_results and retry_count < 3:
                logging.info(f"No results found for {conversation_id}, initiating automatic retry #{retry_count+1}")

                # If this is already a retry, increment the count
                new_retry_count = retry_count + 1

                # Prepare retry data
                retry_data = {
                    'message': user_message,
                    'conversation_id': conversation_id,
                    'timestamp': timestamp,
                    'retry_count': new_retry_count,
                    'is_retry': True
                }

                # Update request.json for recursive call
                request.json = retry_data

                # Only show "searching for alternatives" on the first retry
                if new_retry_count == 1:
                    logging.info(f"Sending 'searching alternatives' response for {conversation_id}")
                    return jsonify({
                        "text": "No results found. Automatically searching for alternatives...",
                        "conversation_id": conversation_id,
                        "timestamp": timestamp,
                        "is_searching_alternatives": True,
                        "retry_data": retry_data  # Include retry data for the frontend
                    })
                else:
                    # For subsequent retries, just make the call directly
                    logging.info(f"Making direct recursive call for retry #{new_retry_count} for {conversation_id}")
                    return chat()  # Recursive call with the updated request.json

            # If we've already retried 3 times with no results, add a note to the response
            if contains_no_results and retry_count >= 3:
                logging.info(f"All retries exhausted for {conversation_id}. Adding note to response.")
                response_text += "\n\nI've tried multiple approaches but couldn't find any matching records. Please try rephrasing your question or using different terms."

            # Handle empty/NULL results more gracefully
            if "NULL" in response_text or "0 rows" in response_text:
                response_text = "No records found matching your query. Please verify the item/customer name and try again."

            # Get token usage from response if available, otherwise estimate
            token_usage = {}
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                token_usage = {
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "response_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.prompt_token_count + response.usage_metadata.candidates_token_count
                }
            else:
                # Fall back to estimation if API doesn't provide token counts
                token_usage = estimate_token_usage(enhanced_prompt, response_text)

            # Add token usage as a small footer
            response_text_with_usage = response_text + f"\n\n<span class='token-usage'>Credits used: {token_usage['total_tokens']} tokens</span>"

            return jsonify({
                "text": response_text_with_usage,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "token_usage": token_usage,  # Also include in JSON for potential frontend usage
                "retry_count": retry_count   # Include retry count in response
            })

        except Exception as error:
            logging.error(f"Error sending message to Gemini in chat endpoint for {conversation_id}: {str(error)}", exc_info=True)
            return jsonify({
                "text": "Sorry, I encountered an error processing your request.",
                "error": str(error),
                "conversation_id": conversation_id,
                "timestamp": timestamp
            }), 500

    except Exception as error:
        logging.error(f"Critical error in chat endpoint: {str(error)}", exc_info=True)
        return jsonify({
            "text": "Sorry, I encountered an unexpected error processing your request.",
            "error": str(error),
            "conversation_id": data.get('conversation_id', 'default') if 'data' in locals() else 'unknown',
            "timestamp": data.get('timestamp', int(time.time() * 1000)) if 'data' in locals() else int(time.time() * 1000)
        }), 500

@app.route('/api/chat/audio', methods=['POST'])
def chat_audio():
    logging.info("Audio endpoint hit")
    temp_audio_path = None # Initialize to ensure it exists for finally block
    try:
        data = request.json
        logging.debug(f"Received audio data keys: {list(data.keys()) if data else 'No data'}")

        audio_base64 = data.get('audio')
        text = data.get('text', '') # Text transcription if available from client, or empty
        timestamp = data.get('timestamp', int(time.time()))
        conversation_id = data.get('conversation_id')

        logging.info(f"Audio endpoint hit. Conversation ID: {conversation_id}. Audio provided: {bool(audio_base64)}. Text provided: '{text}'")

        if not conversation_id:
            logging.warning("No conversation_id provided for audio chat.")

        if not audio_base64:
            logging.error(f"No audio data provided. Conversation ID: {conversation_id}")
            return jsonify({"error": "No audio data provided"}), 400

        try:
            logging.debug(f"Decoding base64 audio data. Conversation ID: {conversation_id}")
            audio_data = base64.b64decode(audio_base64)
            logging.info(f"Successfully decoded {len(audio_data)} bytes of audio data. Conversation ID: {conversation_id}")

            # Create a temporary file to store the audio
            with tempfile.NamedTemporaryFile(delete=False, suffix='.webm') as temp_audio: # Assuming webm format
                temp_audio.write(audio_data)
                temp_audio_path = temp_audio.name
            logging.info(f"Saved audio to temporary file: {temp_audio_path}. Conversation ID: {conversation_id}")

            active_chat_session_data = chat_sessions.get(conversation_id)
            current_history_for_reset = []
            current_turn_count = 0
            is_new_conversation = False

            if active_chat_session_data:
                active_chat_session = active_chat_session_data['session']
                current_history_for_reset = active_chat_session.history
                current_turn_count = active_chat_session_data.get('turn_count', 0)
                active_chat_session_data['last_activity'] = time.time()
                logging.info(f"Using existing session {conversation_id} for audio, turn count: {current_turn_count}")
            else:
                is_new_conversation = True
                logging.info(f"Starting new conversation (audio): {conversation_id}")

            # --- Session Reset Logic for Audio (Same as /api/chat) ---
            if current_turn_count >= RESET_AFTER_N_TURNS:
                logging.info(f"Reset condition met for {conversation_id} (audio). Current turns: {current_turn_count}. Summarizing and re-initializing.")
                summary_of_old_conversation = generate_summary_from_history(current_history_for_reset)

                new_session_initial_history_parts = []
                if summary_of_old_conversation != "No prior conversation.":
                    new_session_initial_history_parts.append(
                        Content(role="user", parts=[Part.from_text(f"Summary of previous conversation: {summary_of_old_conversation}")])
                    )

                reset_model = create_new_generative_model(system_instruction_sql_assistant)
                active_chat_session = reset_model.start_chat(history=new_session_initial_history_parts)

                chat_sessions[conversation_id] = {
                    'model_instance': reset_model,
                    'session': active_chat_session,
                    'last_activity': time.time(),
                    'created_at': datetime.now().isoformat(),
                    'turn_count': 0
                }
                active_chat_session_data = chat_sessions[conversation_id]
                is_new_conversation = True
                logging.info(f"Conversation {conversation_id} (audio) reset complete. New session started with summary.")
            elif not active_chat_session_data: # If it's a brand new conversation
                model_for_new_session = create_new_generative_model(system_instruction_sql_assistant)
                active_chat_session = model_for_new_session.start_chat()
                chat_sessions[conversation_id] = {
                    'model_instance': model_for_new_session,
                    'session': active_chat_session,
                    'last_activity': time.time(),
                    'created_at': datetime.now().isoformat(),
                    'turn_count': 0
                }
                active_chat_session_data = chat_sessions[conversation_id]

            # Create audio part for Gemini
            audio_part = Part.from_data(
                mime_type="audio/webm", # Assuming webm, adjust if client sends other formats
                data=audio_data
            )

            # Prepare the user message for the current turn (combining client text and strict reminder)
            prompt_text_for_audio = text if text else "Please process this audio message"
            final_prompt_for_gemini = prompt_text_for_audio

            # Add initial context for brand new or reset sessions
            if is_new_conversation:
                current_date = datetime.now().strftime("%Y-%m-%d")
                initial_context_str = f"""Today's date is {current_date}.
You are working with a PostgreSQL database. Use PostgreSQL date functions (EXTRACT, DATE_TRUNC, etc.). Prefer using "WITH" in queries."""
                final_prompt_for_gemini = f"{initial_context_str}\n\n{final_prompt_for_gemini}"
                logging.info(f"Added initial context to first audio prompt for {conversation_id}.")

            # Add the strict adherence reminder for every turn
            final_prompt_for_gemini = f"Strictly apply your core instructions, especially for name lookup, transliteration, and contextual matching. Answer: {final_prompt_for_gemini}"

            # NEW: Ensure thorough name lookup before sending the main prompt
            enhanced_prompt = ensure_thorough_name_lookup(final_prompt_for_gemini, active_chat_session_data['model_instance'], search_tool)
            logging.info(f"Enhanced prompt with thorough name lookup for audio in {conversation_id}")

            logging.debug(f"Sending audio message to Gemini for {conversation_id}. Text included: {enhanced_prompt[:200]}...")

            # Send to Gemini with both audio and text (if any)
            response = active_chat_session.send_message(
                [enhanced_prompt, audio_part], # Send text part first
                tools=[search_tool]
            )
            logging.info(f"Got response from Gemini for audio. Conversation ID: {conversation_id}")

            # Increment turn count AFTER a successful model response
            active_chat_session_data['turn_count'] += 1
            logging.debug(f"Conversation {conversation_id} (audio) turn count incremented to {active_chat_session_data['turn_count']}")

            # Extract text from response (consistent with /api/chat)
            response_text = ""
            if hasattr(response, 'candidates') and response.candidates:
                for candidate in response.candidates:
                    if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                        parts_text = [part.text for part in candidate.content.parts if hasattr(part, 'text')]
                        response_text = "\n".join(parts_text)
                        if response_text: break
            if not response_text and hasattr(response, 'text'): # Fallback
                response_text = response.text
            if not response_text:
                response_text = "Sorry, I couldn't generate a proper response from the audio."

            # Get token usage from response if available, otherwise estimate
            token_usage = {}
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                token_usage = {
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "response_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.prompt_token_count + response.usage_metadata.candidates_token_count
                }
            else:
                # Fall back to estimation if API doesn't provide token counts
                token_usage = estimate_token_usage(enhanced_prompt, response_text)

            # Add token usage as a small footer
            response_text_with_usage = response_text + f"\n\n<span class='token-usage'>Credits used: {token_usage['total_tokens']} tokens</span>"

            return jsonify({
                "success": True,
                "text": response_text_with_usage,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "token_usage": token_usage  # Also include in JSON for potential frontend usage
            })

        except Exception as e:
            logging.error(f"Error processing audio for Conversation ID: {conversation_id}. Error: {e}", exc_info=True)
            return jsonify({"error": f"Failed to process audio: {str(e)}"}), 500
        finally: # Ensure temp file cleanup regardless of success/failure
            if temp_audio_path and os.path.exists(temp_audio_path):
                try:
                    os.unlink(temp_audio_path)
                    logging.info(f"Successfully deleted temporary audio file: {temp_audio_path}. Conversation ID: {conversation_id}")
                except Exception as cleanup_error:
                    logging.error(f"Error deleting temporary audio file: {temp_audio_path}. Error: {cleanup_error}")

    except Exception as e:
        logging.error(f"Critical error in /api/chat/audio endpoint. Error: {e}", exc_info=True)
        return jsonify({
            "success": False, # Changed to False for overall error
            "text": "An unexpected server error occurred.",
            "conversation_id": data.get('conversation_id', 'N/A') if data else 'N/A',
            "error": str(e) # Expose error for debugging in development, hide in production
        }), 500


# New endpoint for executing SQL queries
@app.route('/api/execute-sql', methods=['POST'])
def execute_sql():
    try:
        data = request.json
        query = data.get('query', '')

        if not query:
            return jsonify({"error": "No query provided"}), 400

        # Extract SQL query from the markdown code block if present
        if '```sql' in query:
            query = query.split('```sql')[1].split('```')[0].strip()

        conn = get_db_connection()
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(query)
                normalized_query = query.strip().upper()
                if normalized_query.startswith('SELECT') or normalized_query.startswith('WITH'):
                    results = cur.fetchall()
                    return jsonify({
                        "success": True,
                        "results": results,
                        "rowCount": len(results)
                    })
                else:
                    conn.commit()
                    return jsonify({
                        "success": True,
                        "message": "Query executed successfully",
                        "rowCount": cur.rowcount
                    })
        except Exception as e:
            conn.rollback()
            logging.error(f"SQL execution error for query: '{query[:100]}...'. Error: {e}", exc_info=True)
            return jsonify({
                "success": False,
                "error": str(e),
                "errorType": type(e).__name__
            }), 400
        finally:
            conn.close()

    except Exception as e:
        logging.error(f"Error in execute-sql endpoint: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "model": MODEL_NAME})

# Add a direct endpoint for retrying queries
@app.route('/api/retry-query', methods=['POST'])
def retry_query():
    try:
        data = request.json
        original_query = data.get('query', '')
        retry_count = data.get('retry_count', 1)
        conversation_id = data.get('conversation_id', 'default')

        logging.info(f"Retry endpoint hit. Query: '{original_query[:50]}...', Retry Count: {retry_count}")

        if not original_query:
            return jsonify({"error": "No query provided"}), 400

        # Create a new model for this retry
        new_model = create_new_generative_model(system_instruction_sql_assistant)
        active_chat_session = new_model.start_chat()

        # Modify the prompt based on retry count
        retry_instructions = [
            "Try a different approach to find this information. The previous query returned no results.",
            "The previous search found no results. Try alternative spellings, transliterations, or broader terms.",
            "No results were found previously. Try a completely different approach or SQL query structure."
        ]

        # Use different instructions based on retry count
        retry_instruction = retry_instructions[min(retry_count-1, len(retry_instructions)-1)]

        # Create the retry prompt
        retry_prompt = f"{retry_instruction}\n\nOriginal question: {original_query}"

        # Send to Gemini
        response = active_chat_session.send_message(
            retry_prompt,
            tools=[search_tool]
        )

        # Extract text from response
        response_text = ""
        if hasattr(response, 'candidates') and response.candidates:
            for candidate in response.candidates:
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    parts_text = [part.text for part in candidate.content.parts if hasattr(part, 'text')]
                    response_text = "\n".join(parts_text)
                    break
        if not response_text and hasattr(response, 'text'):
            response_text = response.text

        # Estimate token usage
        token_usage = estimate_token_usage(retry_prompt, response_text)

        return jsonify({
            "text": response_text,
            "conversation_id": conversation_id,
            "timestamp": int(time.time() * 1000),
            "token_usage": token_usage,
            "retry_count": retry_count
        })

    except Exception as e:
        logging.error(f"Error in retry-query endpoint: {e}", exc_info=True)
        return jsonify({
            "error": str(e),
            "text": "Sorry, I encountered an error while retrying your query."
        }), 500

# Run the app
if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000, debug=True)
