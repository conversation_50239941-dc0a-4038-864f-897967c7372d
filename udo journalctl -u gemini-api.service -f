[0;1;32m●[0m gemini-api.service - Gemini Chat API Service
     Loaded: loaded (/etc/systemd/system/gemini-api.service; enabled; vendor preset: enabled)
     Active: [0;1;32mactive (running)[0m since Sun 2025-05-25 10:02:59 UTC; 1s ago
   Main PID: 702031 (python3)
     CGroup: /system.slice/gemini-api.service
             └─702031 /usr/bin/python3 /home/<USER>/apps/new_try/image-gemini-chat/api/app.py

May 25 10:02:59 fic systemd[1]: Started Gemini Chat API Service.
