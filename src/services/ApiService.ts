// In /home/<USER>/apps/new_try/image-gemini-chat/src/services/ApiService.ts

interface SqlExecutionResponse {
  success: boolean;
  results?: any[];
  rowCount?: number;
  error?: string;
}

interface AudioMessageResponse {
  success: boolean;
  text: string;
  conversation_id?: string;
  error?: string;
}

interface ChatResponse {
  text: string;
  conversation_id: string;
  timestamp: number;
  success?: boolean; // Add success property
  error?: string;    // Add error property
  token_usage?: {
    prompt_tokens: number;
    response_tokens: number;
    total_tokens: number;
  };
}

export class ApiService {
  static async sendMessage(content: string, timestamp: number, conversationId?: string): Promise<ChatResponse> {
    // تأكد من أن هذا هو تطبيقك الصحيح
    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: content, timestamp, conversation_id: conversationId }),
    });
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async sendAudioMessage(audioBase64: string, text: string, timestamp: number, conversationId?: string): Promise<ChatResponse> {
    console.log('Sending audio message, base64 length:', audioBase64.length);
    try {
      console.log('Making request to /api/chat/audio');
      // Should match your Flask API endpoint
      const response = await fetch('/api/chat/audio', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          audio: audioBase64, 
          text,
          timestamp, 
          conversation_id: conversationId 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error sending audio message:', error);
      return { 
        success: false, 
        text: '',
        conversation_id: conversationId || '',
        timestamp: timestamp,
        error: error instanceof Error ? error.message : 'Failed to send audio message' 
      };
    }
  }

  static async executeSql(query: string): Promise<SqlExecutionResponse> {
    try {
      const response = await fetch('/api/execute-sql', { // تأكد من أن هذا هو مسار API الصحيح
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json(); // قم بتحليل JSON دائمًا

      if (!response.ok) {
        // حتى لو لم تكن الاستجابة ok، قد يحتوي 'data' على رسالة خطأ من الخادم
        return { success: false, error: data.error || `HTTP error! status: ${response.status}` };
      }
      // إذا كانت الاستجابة ok، افترض أن 'data' يحتوي على 'results' و 'rowCount'
      return { success: true, results: data.results, rowCount: data.rowCount };
    } catch (error: any) {
      console.error('ApiService.executeSql runtime error:', error);
      return { success: false, error: error.message || 'Failed to execute SQL query due to a network or runtime error' };
    }
  }
}
