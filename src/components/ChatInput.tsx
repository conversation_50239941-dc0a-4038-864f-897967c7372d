
import { useState, FormEvent, useRef, useEffect } from "react";
import { Send, Mic, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";

interface ChatInputProps {
  onSubmit: (message: string, audioBlob?: Blob) => void;
  disabled?: boolean;
}

export default function ChatInput({ onSubmit, disabled = false }: ChatInputProps) {
  const [input, setInput] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [isAudioSupported, setIsAudioSupported] = useState(false);

  // Check if audio recording is supported
  useEffect(() => {
    setIsAudioSupported(!!(navigator.mediaDevices && window.MediaRecorder));
  }, []);

  const handleSubmit = (e?: FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    if ((input.trim() || audioChunksRef.current.length > 0) && !disabled) {
      if (audioChunksRef.current.length > 0) {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        onSubmit(input, audioBlob);
      } else {
        onSubmit(input);
      }
      setInput("");
      audioChunksRef.current = [];
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const startRecording = async () => {
    console.log('Starting recording...');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('Got user media stream');
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        console.log('Data available event:', event.data.size, 'bytes');
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        console.log('MediaRecorder.onstop: Fired. Processing stop.');
        stream.getTracks().forEach(track => {
          console.log(`MediaRecorder.onstop: Stopping track - kind: ${track.kind}, label: ${track.label}, id: ${track.id}`);
          track.stop();
        });

        setIsRecording(false);

        // Automatically submit if there's audio and not disabled
        // Automatically submit if not disabled
        if (!disabled) {
          console.log('MediaRecorder.onstop: Auto-submitting...');
          handleSubmit();
        } else {
          console.log('MediaRecorder.onstop: Input is disabled, not auto-submitting.');
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        toast.error('Error during recording');
      };

      mediaRecorder.start(1000); // Request data every second
      console.log('MediaRecorder started');
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast.error("Could not access microphone. Please check permissions.");
    }
  };

  const stopRecording = () => {
    console.log('Stopping recording...');
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      console.log('Calling mediaRecorder.stop()');
      try {
        mediaRecorderRef.current.stop(); // This will trigger the onstop handler
        // setIsRecording(false), toast, and audio chunk logging are now primarily in onstop
      } catch (error) {
        console.error('Error stopping recording:', error);
        toast.error('Error stopping recording');
        setIsRecording(false); // Fallback: ensure recording state is reset if .stop() itself errors
      }
    } else {
      console.warn('No active recording to stop');
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };
  
  return (
    <form 
      onSubmit={handleSubmit} 
      className="bg-white p-4 w-full"
    >
      <div className="max-w-3xl mx-auto flex items-center relative">
        <Textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Ask a question about your database..."
          disabled={disabled}
          className={cn(
            "resize-none pl-14 pr-14 py-3 bg-white border border-gray-300 rounded-full",
            "focus-visible:ring-gemini-gold text-gemini-darkGreen placeholder-gray-500",
            "min-h-[48px] max-h-[120px]"
          )}
        />
        
        {isAudioSupported && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  size="icon"
                  onClick={toggleRecording}
                  disabled={disabled || isProcessing}
                  className={cn(
                    "absolute left-2 h-9 w-9 rounded-full transition-colors",
                    isRecording 
                      ? "bg-red-500 hover:bg-red-600 text-white animate-pulse" 
                      : "bg-gray-200 hover:bg-gray-300 text-gray-700"
                  )}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : isRecording ? (
                    <div className="h-3 w-3 rounded-full bg-white" />
                  ) : (
                    <Mic className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isRecording ? "Stop recording" : "Record audio message"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        
        <Button
          type="submit"
          size="icon"
          disabled={(!input.trim() && audioChunksRef.current.length === 0) || disabled || isProcessing}
          className={cn(
            "absolute right-2 h-9 w-9 rounded-full transition-colors",
            "bg-gemini-gold hover:bg-gemini-lightGold text-white"
          )}
        >
          <Send size={16} className="transition-opacity" />
        </Button>
      </div>
    </form>
  );
}
