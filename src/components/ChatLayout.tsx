
import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { <PERSON>u, CreditCard } from "lucide-react";
import { 
  Sidebar, 
  SidebarContent, 
  SidebarHeader, 
  SidebarFooter,
  SidebarMenu,
  SidebarProvider,
  SidebarTrigger
} from "@/components/ui/sidebar";
import Chat from "./Chat";
import Sidebar2 from "./Sidebar";

export default function ChatLayout() {
  const [activeConversation, setActiveConversation] = useState("new");
  const [conversations, setConversations] = useState<{id: string; title: string}[]>([
    { id: "1", title: "Database performance analysis" },
    { id: "2", title: "Sales report Q2 2023" },
    { id: "3", title: "Inventory forecast" },
    { id: "4", title: "Customer segmentation query" },
  ]);
  const [totalTokensUsed, setTotalTokensUsed] = useState(0);
  const isMobile = useIsMobile();
  
  // Debug logging for token updates
  useEffect(() => {
    console.log("Total tokens used updated:", totalTokensUsed);
  }, [totalTokensUsed]);
  
  const handleNewChat = () => {
    const newId = Date.now().toString();
    const newConversation = {
      id: newId,
      title: `New Chat ${conversations.length + 1}`
    };
    
    setConversations([newConversation, ...conversations]);
    setActiveConversation(newId);
  };

  // Function to update total tokens used
  const updateTokensUsed = (tokens: number) => {
    console.log("Updating tokens used:", tokens);
    setTotalTokensUsed(prev => {
      const newTotal = prev + tokens;
      console.log("New total tokens:", newTotal);
      return newTotal;
    });
  };
  
  return (
    <SidebarProvider defaultOpen={!isMobile}>
      <div className="flex h-screen bg-white overflow-hidden w-full">
        <Sidebar2 
          onNewChat={handleNewChat}
          conversations={conversations}
          activeConversation={activeConversation}
          setActiveConversation={setActiveConversation}
        />
        
        <main className="flex-1 flex flex-col h-screen overflow-hidden">
          {/* Make sure the header is always visible with fixed height and z-index */}
          <div className="h-14 min-h-[56px] border-b border-gray-200 flex items-center justify-between px-4 shadow-sm bg-white sticky top-0 z-10">
            <div className="flex items-center">
              <SidebarTrigger className="mr-2 md:hidden">
                <Menu size={20} />
              </SidebarTrigger>
              <h1 className="text-lg font-semibold text-gemini-darkGreen">Future Intelligence | Ask Ur Data</h1>
            </div>
            
            <div className="flex items-center text-xs text-gray-500">
              <CreditCard size={14} className="mr-1 text-gemini-gold" />
              <span>Credits used: {totalTokensUsed.toLocaleString()}</span>
            </div>
          </div>
          <Chat 
            conversationId={activeConversation} 
            onNewChat={handleNewChat}
            onTokensUsed={updateTokensUsed}
          />
        </main>
      </div>
    </SidebarProvider>
  );
}
