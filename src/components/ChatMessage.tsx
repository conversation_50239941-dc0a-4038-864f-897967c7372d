import React from 'react';
import { cn } from "@/lib/utils";
import { Bot, User } from "lucide-react";

type MessageType = "user" | "assistant" | "system";

interface ChatMessageProps {
  type: MessageType;
  content: string;
  isLoading?: boolean;
  timestamp: number;
  audioBlob?: Blob;
  onPlayAudio?: (audioBlob: Blob) => void;
}

export default function ChatMessage({ 
  type, 
  content, 
  isLoading = false, 
  timestamp, 
  audioBlob, 
  onPlayAudio 
}: ChatMessageProps) {
  // Extract token usage from content if present
  const hasTokenUsage = content.includes('<span class=\'token-usage\'>');
  let displayContent = content;
  let tokenUsage = '';
  
  if (hasTokenUsage) {
    const parts = content.split('<span class=\'token-usage\'>');
    displayContent = parts[0].trim();
    if (parts.length > 1) {
      tokenUsage = parts[1].replace('</span>', '').trim();
    }
  }

  return (
    <div className="flex justify-center w-full px-4 py-2">
      <div className={cn(
        "w-full max-w-4xl rounded-lg p-4",
        type === "assistant" ? "bg-white shadow-sm" : "bg-gray-50 shadow-sm"
      )}>
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            {type === "assistant" ? (
              <div className="w-10 h-10 rounded-full bg-gemini-gold flex items-center justify-center">
                <Bot size={20} className="text-white" />
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full bg-gemini-darkGreen flex items-center justify-center">
                <User size={20} className="text-white" />
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <p className="text-sm font-semibold text-gemini-darkGreen">
                {type === "assistant" ? "Assistant" : "You"}
              </p>
              <span className="text-xs text-gray-500">
                {new Date(timestamp).toLocaleTimeString()}
              </span>
            </div>
            
            {isLoading ? (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 rounded-full bg-gemini-gold animate-pulse"></div>
                <div className="w-2 h-2 rounded-full bg-gemini-gold animate-pulse [animation-delay:0.2s]"></div>
                <div className="w-2 h-2 rounded-full bg-gemini-gold animate-pulse [animation-delay:0.4s]"></div>
              </div>
            ) : (
              <div className="space-y-2">
                {audioBlob && onPlayAudio && (
                  <div>
                    <button
                      onClick={() => onPlayAudio(audioBlob)}
                      className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      <svg className="w-4 h-4 text-gemini-gold" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      <span>Play Audio</span>
                    </button>
                  </div>
                )}
                {displayContent.split('\n').map((line, i) => (
                  <p key={i} className="whitespace-pre-wrap text-gray-700">
                    {line}
                  </p>
                ))}
                
                {hasTokenUsage && (
                  <div className="token-usage">
                    {tokenUsage}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
