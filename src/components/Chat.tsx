
import React, { useEffect, useRef, useState } from "react"; // Added React import
import ChatMessage from "./ChatMessage";
import ChatInput from "./ChatInput"; // Added ChatInput import
import EmptyState from "./EmptyState";
import { ApiService } from "@/services/ApiService";
import { SqlResults } from './SqlResults'; // Import SqlResults
import type { SingleResultSet } from './SqlResults'; // Import SingleResultSet type

export type Message = {
  id: string;
  type: "user" | "assistant" | "system";
  content: string;
  timestamp: number;
  audioBlob?: Blob; // Add audio blob for audio messages
  sqlResultSets?: SingleResultSet[]; // Add optional property for SQL results
};

interface ChatProps {
  conversationId: string;
  onNewChat: () => void;
  onTokensUsed?: (tokens: number) => void;
  // Add audio context if needed
  audioContext?: AudioContext; 
}

export default function Chat({ conversationId, onNewChat, onTokensUsed }: ChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [apiConversationId, setApiConversationId] = useState<string>(conversationId);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Reset messages and API conversation ID when conversation changes
    setMessages([]);
    setApiConversationId(conversationId);
  }, [conversationId]);
  
  useEffect(() => {
    // Scroll to bottom on new messages
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);
  
  // Listen for custom send-message events from example buttons
  useEffect(() => {
    const handleSendMessageEvent = (e: CustomEvent) => {
      const message = e.detail;
      if (typeof message === 'string' && message.trim()) {
        handleSendMessage(message);
      }
    };
    
    window.addEventListener('send-message', handleSendMessageEvent as EventListener);
    
    return () => {
      window.removeEventListener('send-message', handleSendMessageEvent as EventListener);
    };
  }, []);
  
  const handleSendMessage = async (content: string, audioBlob?: Blob) => {
    // Ensure there's always some text content, even for audio messages
    const textContent = audioBlob
      ? (content.trim() || "[Audio message]")
      : content;
      
    console.log('handleSendMessage called with:', { hasAudio: !!audioBlob, content: textContent });
    // Add user message
    const timestamp = Date.now();
    const userMessage: Message = {
      id: timestamp.toString(),
      type: "user",
      content: textContent,
      timestamp,
      audioBlob: audioBlob || undefined, // Explicitly include audioBlob
    };
    
    console.log('Adding user message to UI');
    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);
    
    try {
      let response;
      if (audioBlob) {
        console.log('Processing audio message, blob size:', audioBlob.size, 'type:', audioBlob.type);
        // Convert Blob to base64 for sending
        const audioBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = (reader.result as string).split(',')[1];
            resolve(base64data);
          };
          reader.onerror = reject;
          reader.readAsDataURL(audioBlob);
        });
        
        response = await ApiService.sendAudioMessage(audioBase64, content, timestamp, apiConversationId);
      } else {
        // Regular text message
        response = await ApiService.sendMessage(content, timestamp, apiConversationId);
      }
      
      // Store the conversation ID from the response
      if (response.conversation_id) {
        setApiConversationId(response.conversation_id);
      }
      
      // Extract token usage from response if available
      if (response.token_usage && onTokensUsed) {
        console.log("Token usage received:", response.token_usage.total_tokens);
        onTokensUsed(response.token_usage.total_tokens);
      } else {
        console.log("No token usage in response:", response);
      }
      
      // Process the response text
      let assistantMessageContent = response.text || "I couldn't generate a response.";
      const originalBotContent = response.text; // Keep original for reference if needed
      const sqlQueries = extractSqlQueries(assistantMessageContent);
      let collectedResultSets: SingleResultSet[] = [];
      let hasSuccessfulQuery = false;

      if (sqlQueries.length > 0) {
        for (const query of sqlQueries) {
          try {
            const sqlExecutionResult = await ApiService.executeSql(query);
            if (sqlExecutionResult.success) {
              collectedResultSets.push({
                query: query,
                results: sqlExecutionResult.results || [],
                rowCount: sqlExecutionResult.rowCount || 0,
              });
              hasSuccessfulQuery = true; // Mark that at least one query was successful
            } else {
              collectedResultSets.push({
                query: query,
                results: [],
                rowCount: 0,
                error: sqlExecutionResult.error || 'Unknown error executing SQL',
              });
            }
          } catch (error: any) {
            console.error("Error executing individual SQL query:", error);
            collectedResultSets.push({
              query: query,
              results: [],
              rowCount: 0,
              error: error.message || 'Failed to execute SQL query',
            });
          }
        }
      }

      // If SQL queries were found and at least one was successful, modify the displayed content
      if (sqlQueries.length > 0 && hasSuccessfulQuery) {
        const firstSqlBlockIndex = originalBotContent.indexOf('```sql');
        if (firstSqlBlockIndex > 0) {
          // Use the text before the first SQL block
          assistantMessageContent = originalBotContent.substring(0, firstSqlBlockIndex).trim();
          // If the preceding text is empty or too short, use a generic message
          if (!assistantMessageContent || assistantMessageContent.length < 10) { 
            assistantMessageContent = "Here are the results for your query:";
          }
        } else {
          // Default message if no text precedes the SQL block or no SQL block found (though sqlQueries.length > 0 implies it was found by regex)
          assistantMessageContent = "Here are the results for your query:";
        }
      } else if (sqlQueries.length > 0 && !hasSuccessfulQuery) {
        // All SQL queries failed, keep original content which might contain error messages from Gemini
        assistantMessageContent = originalBotContent;
      } else {
        // No SQL queries found, use original content
        assistantMessageContent = originalBotContent;
      }
      
      const assistantMessage: Message = {
        id: Date.now().toString(),
        type: "assistant",
        content: assistantMessageContent, // This is now the potentially modified content
        timestamp: Date.now(),
        sqlResultSets: collectedResultSets.length > 0 ? collectedResultSets : undefined,
      };
      
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString(),
        type: "assistant",
        content: "Sorry, I encountered an error processing your request. Please try again later.",
        timestamp: Date.now(),
      };
      
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Always render the chat interface, even when empty
  // Handle play audio for audio messages
  const handlePlayAudio = (audioBlob: Blob) => {
    const audioUrl = URL.createObjectURL(audioBlob);
    const audio = new Audio(audioUrl);
    audio.play().catch(error => {
      console.error('Error playing audio:', error);
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >  
        {messages.length === 0 ? (
          <EmptyState onNewChat={onNewChat} />
        ) : (
          <div className="gemini-scrollbar" ref={messagesContainerRef}>
            {messages.map((message) => (
              <React.Fragment key={message.id}>
                <ChatMessage
                  key={message.id}
                  type={message.type}
                  content={message.content}
                  audioBlob={message.audioBlob}
                  onPlayAudio={handlePlayAudio}
                  timestamp={message.timestamp}
                />
                {/* Render SqlResults component if sqlResultSets exist for this assistant message */}
                {message.type === "assistant" && message.sqlResultSets && message.sqlResultSets.length > 0 && (
                  <div className="px-4 pb-4"> {/* Added padding for SqlResults display */}
                    <SqlResults resultSets={message.sqlResultSets} />
                  </div>
                )}
              </React.Fragment>
            ))}
            
            {isLoading && (
              <ChatMessage 
                type="assistant" 
                content="" 
                timestamp={Date.now()} // Added timestamp for loading state message
                isLoading={true}
              />
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Fixed chat input at the bottom of the screen, always visible */}
      <div className="sticky bottom-0 left-0 right-0 w-full border-t border-gray-200 bg-white">
        <ChatInput 
          onSubmit={handleSendMessage} 
          disabled={isLoading}
        />
      </div>
    </div>
  );
}

// Helper function to extract SQL queries from text
function extractSqlQueries(text: string): string[] {
  const queries: string[] = [];
  // Regex to find SQL blocks, allows for optional 'sql' after ```
  const regex = /```(?:sql)?\s*([\s\S]*?)\s*```/gi; // Corrected regex - removed double backslash for \s
  let match;
  while ((match = regex.exec(text)) !== null) {
    // Ensure we're capturing the content inside the backticks
    if (match[1]) {
      queries.push(match[1].trim());
    }
  }
  return queries;
}
