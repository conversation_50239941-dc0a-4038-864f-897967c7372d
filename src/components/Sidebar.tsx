import React, { useState } from "react";
import { LogOut, MessageCircle, PlusCircle, Settings, User, Pin } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { 
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  useSidebar
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

export default function SidebarComponent({ 
  onNewChat, 
  conversations, 
  activeConversation, 
  setActiveConversation 
}: { 
  onNewChat: () => void; 
  conversations: { id: string; title: string }[];
  activeConversation: string;
  setActiveConversation: (id: string) => void;
}) {
  const { open } = useSidebar();
  const [pinnedChats, setPinnedChats] = useState<string[]>([]);
  // Assuming gemini-gold is defined in your tailwind.config.js
  // For example: colors: { 'gemini-gold': '#FFD700', /* or your specific gold hex */ }
  
  const handleLogout = () => {
    console.log("User logged out");
    // Add actual logout logic here when you implement authentication
  };

  const handleSettings = () => {
    console.log("Opening settings");
    // Add settings functionality here
  };

  const togglePin = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setPinnedChats(prev => 
      prev.includes(chatId) 
        ? prev.filter(id => id !== chatId)
        : [...prev, chatId]
    );
  };

  // Sort conversations: pinned first, then others
  const sortedConversations = [...conversations].sort((a, b) => {
    const aPinned = pinnedChats.includes(a.id);
    const bPinned = pinnedChats.includes(b.id);
    if (aPinned && !bPinned) return -1;
    if (!aPinned && bPinned) return 1;
    return 0;
  });
  
  return (
    <Sidebar variant="inset" collapsible="offcanvas" className="border-r-2 border-gemini-gold/50">
      {/* Added border-r-2 border-gemini-gold/50 for a right golden border */}
      {/* You can adjust thickness (e.g., border-r) and opacity (e.g., border-gemini-gold/75) */}
      <div className="h-full flex flex-col bg-gemini-darkGreen/90 text-white backdrop-blur-sm">
        <SidebarHeader className="p-4 border-b border-gemini-mediumGreen/30">
          {/* Consider changing this border to gold too if desired: border-gemini-gold/30 */}
          <h2 className="text-lg font-bold flex items-center gap-2 mb-4">
            <div className="w-8 h-8 rounded-full bg-gemini-gold flex items-center justify-center">
              <span className="text-gemini-darkGreen font-bold">FI</span>
            </div>
            <span>Future Intelligence</span>
          </h2>
          <Button 
            onClick={onNewChat}
            className="w-full bg-transparent hover:bg-gemini-mediumGreen text-white border border-white/30 flex items-center gap-2"
          >
            <PlusCircle size={18} />
            <span>New chat</span>
          </Button>
        </SidebarHeader>
        
        <SidebarContent className="flex-1 overflow-auto gemini-scrollbar p-0">
          <SidebarGroup>
            <SidebarGroupLabel className="text-white/70 px-3 py-2 flex items-center">
              <MessageCircle size={16} className="mr-2" />
              <span>Chat History</span>
            </SidebarGroupLabel>
            
            <SidebarGroupContent>
              <SidebarMenu>
                {sortedConversations.map((chat) => (
                  <SidebarMenuItem key={chat.id} className="relative group">
                    <SidebarMenuButton
                      className={cn(
                        "w-full justify-start text-left font-normal pl-2\.5",
                        activeConversation === chat.id
                          ? "bg-gemini-mediumGreen/80 text-white" // Slightly adjusted active background
                          : "hover:bg-gemini-mediumGreen/50 text-white/80"
                      )}
                      onClick={() => setActiveConversation(chat.id)}
                    >
                      <span className="truncate">{chat.title}</span>
                      <button
                        onClick={(e) => togglePin(chat.id, e)}
                        className={cn(
                          "absolute right-2 p-1 rounded-full transition-colors duration-200",
                          pinnedChats.includes(chat.id)
                            ? "text-gemini-gold hover:text-gemini-gold/80"
                            : "text-white/40 hover:text-white/60 group-hover:text-gemini-gold/70" // Pin icon becomes gold on item hover
                        )}
                      >
                        <Pin size={14} className={cn(
                          "transition-transform duration-200",
                          pinnedChats.includes(chat.id) && "rotate-45"
                        )} />
                      </button>
                    </SidebarMenuButton>
                    {activeConversation === chat.id && (
                      <div className="absolute right-0 top-0 bottom-0 w-1.5 bg-gemini-gold rounded-l-sm" /> 
                      // Made active indicator thicker, gold, and slightly rounded
                    )}
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        
        <SidebarFooter className="p-4 border-t border-gemini-mediumGreen/30">
          {/* Consider changing this border to gold too if desired: border-gemini-gold/30 */}
          <div className="flex items-center justify-between">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center gap-3 hover:bg-gemini-mediumGreen/50 rounded-md p-0 transition-colors duration-200">
                  <div className="w-8 h-8 rounded-full bg-gemini-gold flex items-center justify-center">
                    <User size={16} className="text-gemini-darkGreen" />
                  </div>
                  <span className="text-white">admin</span>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="min-w-[200px]" align="start">
                <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                  <LogOut size={16} className="mr-2" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <Button 
            variant="ghost" 
            className="w-full justify-start mt-2 text-white hover:bg-gemini-mediumGreen/50"
            onClick={handleSettings}
          >
            <Settings size={16} className="mr-2" />
            <span>Settings</span>
          </Button>
        </SidebarFooter>
      </div>
    </Sidebar>
  );
}
