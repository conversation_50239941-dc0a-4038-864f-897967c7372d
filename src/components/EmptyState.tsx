
import { Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface EmptyStateProps {
  onNewChat: () => void;
}

export default function EmptyState({ onNewChat }: EmptyStateProps) {
  const examples = [
    "Show me revenue trends for Q1 2023",
    "Compare sales by region for the last quarter",
    "What are the top performing products?",
    "Generate a report of overdue invoices"
  ];
  
  // Function to handle example clicks
  const handleExampleClick = (example: string) => {
    onNewChat();
    // We need to add a small delay to allow for the new chat to be created before sending the message
    setTimeout(() => {
      // Dispatch a custom event that Chat.tsx will listen for
      window.dispatchEvent(new CustomEvent('send-message', { detail: example }));
    }, 100);
  };

  return (
    <div className="flex flex-col items-center pt-10 pb-20 max-w-md mx-auto px-3 sm:px-4 text-center bg-white">
      <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-gemini-gold/20 flex items-center justify-center mb-3 sm:mb-4">
        <Sparkles size={24} className="text-gemini-gold" />
      </div>
      
      <h1 className="text-xl sm:text-2xl font-semibold mb-2 text-gemini-darkGreen">Database Assistant</h1>
      
      <p className="text-xs sm:text-sm text-gemini-darkGreen/70 mb-3 sm:mb-4">
        Ask me questions about your database or try one of these examples:
      </p>
      
      <div className="grid gap-2 w-full mb-4 sm:mb-6">
        {examples.map((example, i) => (
          <Button 
            key={i}
            variant="outline"
            className="justify-start text-left h-auto p-3 sm:p-4 text-xs sm:text-sm bg-gray-50 border-gray-200 hover:bg-gray-100 text-gemini-darkGreen"
            onClick={() => handleExampleClick(example)}
          >
            {example}
          </Button>
        ))}
      </div>
      
      <Button 
        onClick={onNewChat}
        className="bg-gemini-gold hover:bg-gemini-lightGold text-white transition-colors duration-300"
      >
        <Sparkles size={16} className="mr-2" />
        Start New Chat
      </Button>
    </div>
  );
}
