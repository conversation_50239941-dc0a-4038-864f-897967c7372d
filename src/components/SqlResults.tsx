import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { cn } from '@/lib/utils';

// Interface for a single result set (one table)
export interface SingleResultSet { // Added export here
  query?: string; // The SQL query string, optional
  results: any[];
  rowCount: number;
  error?: string;
}

// Props for the main SqlResults component, now accepts an array of result sets
interface SqlResultsProps {
  resultSets: SingleResultSet[];
}

// New internal component to render a single table
function SingleTableView({ resultSet }: { resultSet: SingleResultSet }) {
  const { results, rowCount, error, query } = resultSet;
  const [showAll, setShowAll] = useState(false);

  console.log(`[SingleTableView] Rendering for query (or index if no query): ${query || 'N/A'}. Current showAll: ${showAll}`);

  if (error) {
    return (
      <div className="mb-6">
        {query && <pre className="text-xs bg-gray-100 p-2 rounded-md overflow-x-auto mb-2">{query}</pre>}
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 font-medium">Error executing query:</p>
          <p className="text-red-500 mt-2">{error}</p>
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="mb-6">
        {query && <pre className="text-xs bg-gray-100 p-2 rounded-md overflow-x-auto mb-2">{query}</pre>}
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-gray-600">No results found for this query.</p>
        </div>
      </div>
    );
  }

  const columns = Object.keys(results[0]);
  const rowsToDisplay = showAll ? results : results.slice(0, 5);

  return (
    <div className="mx-auto border border-gemini-gold rounded-lg mb-6">
      {query && (
        <div className="p-2 bg-gray-100 border-b border-gemini-gold/20">
          <p className="text-xs font-mono text-gray-700">Query:</p>
          <pre className="text-xs text-gray-600 overflow-x-auto">{query}</pre>
        </div>
      )}
      <div className="p-4 bg-gray-50 border-b border-gemini-gold/50 flex justify-between items-center">
        <p className="text-sm text-gray-600">
          {showAll || results.length <= 5 ? rowCount : Math.min(rowCount, 5)} of {rowCount} {rowCount === 1 ? 'row' : 'rows'}
        </p>
        {results.length > 5 && (
          <Button 
            variant="link" 
            onClick={() => {
              console.log(`[SingleTableView] Button clicked for ${query || 'N/A'}. Current showAll before toggle: ${showAll}`);
              setShowAll(prevShowAll => !prevShowAll);
            }} 
            className="text-sm h-auto p-0"
          >
            {showAll ? "Show Less" : "View All"}
          </Button>
        )}
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column} className="bg-gray-50 text-gray-700 sticky top-0 z-10">
                {column}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {rowsToDisplay.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              className={cn(
                rowIndex % 2 === 0 ? 'bg-gemini-gold/10' : 'bg-gemini-mediumGreen/10'
              )}
            >
              {columns.map((column) => (
                <TableCell key={`${rowIndex}-${column}`} className="font-normal text-gray-800">
                  {formatCellValue(row[column], column)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

// Main exported component
export function SqlResults({ resultSets }: SqlResultsProps) {
  if (!resultSets || resultSets.length === 0) {
    return (
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-gray-600">No SQL results to display.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {resultSets.map((resultSet, index) => (
        <SingleTableView key={index} resultSet={resultSet} />
      ))}
    </div>
  );
}

// formatCellValue and isDateString functions remain the same
// (ensure they are outside the SingleTableView and SqlResults components if not already)

function formatCellValue(value: any, columnName: string): string {
  if (value === null) return 'NULL';
  if (value === undefined) return '';
  
  // Handle dates
  if (value instanceof Date || (typeof value === 'string' && isDateString(value))) {
    try {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (e) {
      console.error('Date parsing error:', e);
    }
  }
  
  // Handle numbers
  if (typeof value === 'number') {
    return value.toLocaleString('en-US');
  }
  
  // Handle objects
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  // Handle strings (including Arabic)
  return String(value);
}

function isDateString(value: string): boolean {
  // Check if the string matches common date formats
  const datePatterns = [
    /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO format
    /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
    /^\d{2}-\d{2}-\d{4}$/ // DD-MM-YYYY
  ];
  
  return datePatterns.some(pattern => pattern.test(value));
} 