
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 120 30% 10%;

    --card: 0 0% 98%;
    --card-foreground: 120 30% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 120 30% 10%;

    --primary: 38 55% 63%;
    --primary-foreground: 120 30% 10%;

    --secondary: 120 25% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 120 15% 94%;
    --muted-foreground: 120 10% 40%;

    --accent: 38 55% 63%;
    --accent-foreground: 120 30% 10%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 15% 85%;
    --input: 120 15% 85%;
    --ring: 38 55% 63%;

    --radius: 0.5rem;
    
    --sidebar-background: 150 30% 20%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 38 55% 63%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 150 20% 25%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 150 20% 25%;
    --sidebar-ring: 38 55% 63%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-white text-foreground;
    overflow: hidden;
  }

  .gemini-scrollbar::-webkit-scrollbar {
    width: 5px;
  }
  
  .gemini-scrollbar::-webkit-scrollbar-track {
    background: theme('colors.white');
  }
  
  .gemini-scrollbar::-webkit-scrollbar-thumb {
    background: theme('colors.gemini.gold');
    border-radius: 5px;
  }
  
  .gemini-scrollbar::-webkit-scrollbar-thumb:hover {
    background: theme('colors.gemini.lightGold');
  }

  .typing-dots::after {
    content: '';
    animation: ellipsis 1.5s infinite;
  }
  
  @keyframes ellipsis {
    0% { content: '.'; }
    33% { content: '..'; }
    66% { content: '...'; }
    100% { content: '.'; }
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .responsive-padding {
      @apply px-2;
    }
    
    .responsive-text {
      @apply text-sm;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .responsive-padding {
      @apply px-4;
    }
  }

  @media (min-width: 1025px) {
    .responsive-padding {
      @apply px-6;
    }
  }
}

/* Token usage display */
.token-usage {
  font-size: 0.7rem;
  color: #888;
  opacity: 0.7;
  display: block;
  text-align: right;
  margin-top: 0.5rem;
  font-style: italic;
}
